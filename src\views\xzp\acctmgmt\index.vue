<template>
  <div class="app-container">
    <yo-table
      ref="crud"
      :option="option"
      :data="data"
      v-model="form"
      :table-loading="loading"
      :page.sync="page"
      :search.sync="query"
      @on-load="onLoad"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
    >
      <!-- 自定义查询区域控件 -->
      <template slot="opeCdSearch" slot-scope="{column, size, disabled, readonly}">
        <el-autocomplete
          v-model="query.opeCd"
          :fetch-suggestions="queryOpeCd"
          :placeholder="column.placeholder"
          @select="handleOpeCdSelect"
          @clear="handleOpeCdClear"
          :trigger-on-focus="column.triggerOnFocus"
          :clearable="column.clearable"
          :size="size"
          :disabled="disabled"
          :readonly="readonly"
          style="width: 100%;"
        >
          <template slot-scope="{ item }">
            <div class="autocomplete-item">
              <div class="name">{{ item.value + '-' + item.label }}</div>
            </div>
          </template>
        </el-autocomplete>
      </template>
      <template slot="merchIdSearch" slot-scope="{column, size, disabled, readonly}">
        <el-autocomplete
          v-model="query.merchId"
          :fetch-suggestions="queryMerchId"
          :placeholder="column.placeholder"
          @select="handleMerchIdSelect"
          @clear="handleMerchIdClear"
          :trigger-on-focus="column.triggerOnFocus"
          :clearable="column.clearable"
          :size="size"
          :disabled="disabled"
          :readonly="readonly"
          style="width: 100%;"
        >
          <template slot-scope="{ item }">
            <div class="autocomplete-item">
              <div class="name">{{ item.value + '-' + item.label }}</div>
            </div>
          </template>
        </el-autocomplete>
      </template>
      <!-- 自定义委托单位代码自动完成 -->
      

      <!-- 自定义弹框控件 -->
      <template slot="opeCdForm" slot-scope="{column, size, disabled, readonly}">
        <el-autocomplete
          v-model="form.opeCd"
          :fetch-suggestions="queryOpeCd"
          :placeholder="column.placeholder"
          @select="handleOpeCdFormSelect"
          @clear="handleOpeCdFormClear"
          :trigger-on-focus="column.triggerOnFocus"
          :clearable="column.clearable"
          :size="size"
          :disabled="disabled"
          :readonly="readonly"
          style="width: 100%;"
        >
          <template slot-scope="{ item }">
            <div class="autocomplete-item">
              <div class="name">{{ item.value + '-' + item.label }}</div>
            </div>
          </template>
        </el-autocomplete>
      </template>
      <template slot="merchIdForm" slot-scope="{column, size, disabled, readonly}">
        <el-autocomplete
          v-model="form.merchId"
          :fetch-suggestions="queryMerchId"
          :placeholder="column.placeholder"
          @select="handleMerchIdFormSelect"
          @clear="handleMerchIdFormClear"
          :trigger-on-focus="column.triggerOnFocus"
          :clearable="column.clearable"
          :size="size"
          :disabled="disabled"
          :readonly="readonly"
          style="width: 100%;"
        >
          <template slot-scope="{ item }">
            <div class="autocomplete-item">
              <div class="name">{{ item.value + '-' + item.label }}</div>
            </div>
          </template>
        </el-autocomplete>
      </template>
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-hasPermi="['admin:xzp:acctmgmt:remove']"
          @click="handleDelete"
          :disabled="selectionList.length === 0"
        >
          删除
        </el-button>
      </template>
    </yo-table>
  </div>
</template>

<script>
import { queryAcctInfoList, addAcctInfo, updateAcctInfo, deleteAcctInfo, getAcctInfo } from '@/api/acctmgmt';
import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
import option from './infoData/acctMgmtOption';
import { mapGetters } from 'vuex';

export default {
  name: 'AcctMgmt',
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: option,
      data: [],
      // 自动完成相关数据
      opeCdLoading: false,
      merchIdLoading: false
    };
  },
  computed: {
    ...mapGetters(['permission'])
  },
  methods: {
    // 获取数据
    onLoad(page, params = {}) {
      this.loading = true;
      const query = {
        ...this.query,
        ...params,
        current: page.currentPage,
        size: page.pageSize
      };
      
      queryAcctInfoList(query).then(res => {
        const data = res.data.data || res.data;
        this.data = data.list || data.records || [];
        this.page.total = data.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    // 搜索回调
    searchChange(params, done) {
      // 合并搜索参数，保留自定义字段的值
      this.query = { ...this.query, ...params };
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
      done();
    },
    
    // 搜索重置
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    
    // 选择改变
    selectionChange(list) {
      this.selectionList = list;
    },
    
    // 当前页改变
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    
    // 页大小改变
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    
    // 刷新
    refreshChange() {
      this.onLoad(this.page);
    },
    
    // 新增前回调
    rowSave(row, loading, done) {
      // 设置默认值
      if (!row.transType) {
        row.transType = 'zzg';
      }
      
      addAcctInfo(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: 'success',
          message: '操作成功!'
        });
      }).catch(() => {
        loading();
      });
    },
    
    // 修改前回调
    rowUpdate(row, index, loading, done) {
      updateAcctInfo(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: 'success',
          message: '操作成功!'
        });
      }).catch(() => {
        loading();
      });
    },
    
    // 删除回调
    rowDel(row, index) {
      return deleteAcctInfo(row.id);
    },
    
    // 批量删除
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择要删除的数据');
        return;
      }
      
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const promises = this.selectionList.map(item => deleteAcctInfo(item.id));
        Promise.all(promises).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        }).catch(() => {
          this.$message.error('删除失败');
        });
      });
    },
    
    // 查询业务代码
    async queryOpeCd(queryString, cb) {
      this.opeCdLoading = true;
      try {
        const params = {
          opeCd: queryString
        };
        const { code, data } = await listGroupByOpeCd(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error);
        cb([]);
      } finally {
        this.opeCdLoading = false;
      }
    },

    // 查询委托单位代码
    async queryMerchId(queryString, cb) {
      this.merchIdLoading = true;
      try {
        const params = {
          merchId: queryString
        };
        const { code, data } = await listGroupByMerchId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching merchId:', error);
        cb([]);
      } finally {
        this.merchIdLoading = false;
      }
    },

    // 选择业务代码（搜索区域）
    handleOpeCdSelect(item) {
      this.query.opeCd = item.value;
      this.query.opeNm = item.label;
    },

    // 选择委托单位代码（搜索区域）
    handleMerchIdSelect(item) {
      this.query.merchId = item.value;
      this.query.merchNm = item.label;
    },

    // 清除业务代码（搜索区域）
    handleOpeCdClear() {
      this.query.opeCd = '';
      this.query.opeNm = '';
    },

    // 清除委托单位代码（搜索区域）
    handleMerchIdClear() {
      this.query.merchId = '';
      this.query.merchNm = '';
    },

    // 选择业务代码（弹框表单）
    handleOpeCdFormSelect(item) {
      this.form.opeCd = item.value;
      this.form.opeNm = item.label;
    },

    // 选择委托单位代码（弹框表单）
    handleMerchIdFormSelect(item) {
      this.form.merchId = item.value;
      this.form.merchNm = item.label;
    },

    // 清除业务代码（弹框表单）
    handleOpeCdFormClear() {
      this.form.opeCd = '';
      this.form.opeNm = '';
    },

    // 清除委托单位代码（弹框表单）
    handleMerchIdFormClear() {
      this.form.merchId = '';
      this.form.merchNm = '';
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.autocomplete-item {
  line-height: normal;
  padding: 7px;
}

.autocomplete-item .name {
  text-overflow: ellipsis;
  overflow: hidden;
}

.autocomplete-item .addr {
  font-size: 12px;
  color: #b4b4b4;
}

.autocomplete-item .highlighted .addr {
  color: #ddd;
}
</style>