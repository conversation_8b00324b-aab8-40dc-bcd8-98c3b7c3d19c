<template>
  <div class="app-container">
    <yo-table
      ref="crud"
      :option="option"
      :data="data"
      v-model="form"
      :table-loading="loading"
      :page.sync="page"
      :search.sync="query"
      @on-load="onLoad"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
    >
      <!-- 自定义查询区域控件 -->
      <template slot="opeCdSearch" slot-scope="{column, size, disabled, readonly}">
        <div class="input-with-tip">
          <el-autocomplete
            v-model="query.opeCd"
            :fetch-suggestions="queryOpeCd"
            :placeholder="column.placeholder"
            @select="handleOpeCdSelect"
            @clear="handleOpeCdClear"
            :trigger-on-focus="column.triggerOnFocus"
            :clearable="column.clearable"
            :size="size"
            :disabled="disabled"
            :readonly="readonly"
            class="custom-input"
          >
            <template slot-scope="{ item }">
              <div class="autocomplete-item">
                <div class="name">{{ item.value + '-' + item.label }}</div>
              </div>
            </template>
          </el-autocomplete>
          <span v-if="query.opeNm" class="tip-text">{{ query.opeNm }}</span>
        </div>
      </template>
      <template slot="merchIdSearch" slot-scope="{column, size, disabled, readonly}">
        <div class="input-with-tip">
          <el-autocomplete
            v-model="query.merchId"
            :fetch-suggestions="queryMerchId"
            :placeholder="column.placeholder"
            @select="handleMerchIdSelect"
            @clear="handleMerchIdClear"
            :trigger-on-focus="column.triggerOnFocus"
            :clearable="column.clearable"
            :size="size"
            :disabled="disabled"
            :readonly="readonly"
            class="custom-input"
          >
            <template slot-scope="{ item }">
              <div class="autocomplete-item">
                <div class="name">{{ item.value + '-' + item.label }}</div>
              </div>
            </template>
          </el-autocomplete>
          <span v-if="query.merchNm" class="tip-text">{{ query.merchNm }}</span>
        </div>
      </template>
      <!-- 自定义委托单位代码自动完成 -->
      

      <!-- 自定义弹框控件 -->
      <template slot="opeCdForm" slot-scope="{column, size, disabled, readonly}">
        <el-autocomplete
          v-model="form.opeCd"
          :fetch-suggestions="queryOpeCdForm"
          :placeholder="column.placeholder"
          @select="handleOpeCdFormSelect"
          @clear="handleOpeCdFormClear"
          :trigger-on-focus="column.triggerOnFocus"
          :clearable="column.clearable"
          :size="size"
          :disabled="disabled"
          :readonly="readonly"
          style="width: 100%;"
        >
          <template slot-scope="{ item }">
            <div class="autocomplete-item">
              <div class="name">{{ item.value + '-' + item.label }}</div>
            </div>
          </template>
        </el-autocomplete>
      </template>
      <template slot="merchIdForm" slot-scope="{column, size, disabled, readonly}">
        <el-autocomplete
          v-model="form.merchId"
          :fetch-suggestions="queryMerchIdForm"
          :placeholder="column.placeholder"
          @select="handleMerchIdFormSelect"
          @clear="handleMerchIdFormClear"
          :trigger-on-focus="column.triggerOnFocus"
          :clearable="column.clearable"
          :size="size"
          :disabled="disabled"
          :readonly="readonly"
          style="width: 100%;"
        >
          <template slot-scope="{ item }">
            <div class="autocomplete-item">
              <div class="name">{{ item.value + '-' + item.label }}</div>
            </div>
          </template>
        </el-autocomplete>
      </template>
      <template slot="menuLeft">
        <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          plain
          v-hasPermi="['admin:xzp:acctmgmt:remove']"
          @click="handleDelete"
          :disabled="selectionList.length === 0"
        >
          删除
        </el-button>
      </template>

      <!-- 自定义操作列 -->
      <template slot-scope="scope" slot="menu">
        <el-button
          type="text"
          size="small"
          icon="el-icon-view"
          v-hasPermi="['admin:xzp:acctmgmt:view']"
          @click="handleView(scope.row)"
        >
          查看
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-edit"
          v-hasPermi="['admin:xzp:acctmgmt:edit']"
          @click="handleEdit(scope.row, scope.$index)"
        >
          修改
        </el-button>
        <el-button
          type="text"
          size="small"
          icon="el-icon-delete"
          v-hasPermi="['admin:xzp:acctmgmt:remove']"
          @click="handleSingleDelete(scope.row, scope.$index)"
        >
          删除
        </el-button>
      </template>
    </yo-table>
  </div>
</template>

<script>
import { queryAcctInfoList, addAcctInfo, updateAcctInfo, deleteAcctInfo, getAcctInfo } from '@/api/acctmgmt';
import { listGroupByMerchId, listGroupByOpeCd } from '@/api/merchope';
import option from './infoData/acctMgmtOption';
import { mapGetters } from 'vuex';

export default {
  name: 'AcctMgmt',
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: option,
      data: [],
      // 自动完成相关数据
      opeCdLoading: false,
      merchIdLoading: false
    };
  },
  computed: {
    ...mapGetters(['permission'])
  },
  methods: {
    // 获取数据
    onLoad(page, params = {}) {
      this.loading = true;
      const query = {
        ...this.query,
        ...params,
        current: page.currentPage,
        size: page.pageSize
      };
      
      queryAcctInfoList(query).then(res => {
        const data = res.data.data || res.data;
        this.data = data.list || data.records || [];
        this.page.total = data.total || 0;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    // 搜索回调
    searchChange(params, done) {
      // 合并搜索参数，保留自定义字段的值
      this.query = { ...this.query, ...params };
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
      done();
    },
    
    // 搜索重置
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    
    // 选择改变
    selectionChange(list) {
      this.selectionList = list;
    },
    
    // 当前页改变
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    
    // 页大小改变
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    
    // 刷新
    refreshChange() {
      this.onLoad(this.page);
    },
    
    // 新增前回调
    rowSave(row, loading, done) {
      // 设置默认值
      if (!row.transType) {
        row.transType = 'zzg';
      }
      
      addAcctInfo(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: 'success',
          message: '操作成功!'
        });
      }).catch(() => {
        loading();
      });
    },
    
    // 修改前回调
    rowUpdate(row, index, loading, done) {
      updateAcctInfo(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: 'success',
          message: '操作成功!'
        });
      }).catch(() => {
        loading();
      });
    },
    
    // 删除回调
    rowDel(row, index) {
      return deleteAcctInfo(row.cpabAccId, row.acctNm);
    },
    
    // 单行删除
    handleSingleDelete(row, index) {
      this.$confirm(`确定要删除监管账户"${row.cpabAccId}"的记录吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteAcctInfo(row.cpabAccId, row.acctNm).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.onLoad(this.page);
        }).catch(() => {
          this.$message.error('删除失败');
        });
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 查看详情
    handleView(row) {
      // 可以选择直接使用当前行数据查看，或者调用API获取最新数据
      this.$refs.crud.rowView(row);

      // 如果需要调用API获取最新数据，可以使用以下方式：
      // getAcctInfo(row.id).then(res => {
      //   if (res.code === '0' && res.data) {
      //     this.$refs.crud.rowView(res.data);
      //   }
      // }).catch(() => {
      //   this.$message.error('获取详情失败');
      // });
    },

    // 单行编辑
    handleEdit(row, index) {
      this.form = { ...row };
      this.$refs.crud.rowEdit(row, index);
    },

    // 批量删除
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择要删除的数据');
        return;
      }

      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const promises = this.selectionList.map(item => deleteAcctInfo(item.cpabAccId, item.acctNm));
        Promise.all(promises).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
        }).catch(() => {
          this.$message.error('删除失败');
        });
      });
    },
    
    // 查询业务代码
    async queryOpeCd(queryString, cb) {
      this.opeCdLoading = true;
      try {
        const params = {
          opeCd: queryString
        };
        const { code, data } = await listGroupByOpeCd(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error);
        cb([]);
      } finally {
        this.opeCdLoading = false;
      }
    },

    // 查询委托单位代码（搜索区域）
    async queryMerchId(queryString, cb) {
      this.merchIdLoading = true;
      try {
        const params = {
          merchId: queryString,
          opeCd: this.query.opeCd // 添加业务代码参数进行联动查询
        };
        const { code, data } = await listGroupByMerchId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching merchId:', error);
        cb([]);
      } finally {
        this.merchIdLoading = false;
      }
    },

    // 查询业务代码（弹框表单）
    async queryOpeCdForm(queryString, cb) {
      this.opeCdLoading = true;
      try {
        const params = {
          opeCd: queryString
        };
        const { code, data } = await listGroupByOpeCd(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.opeCd,
            label: item.opeNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching opeCd:', error);
        cb([]);
      } finally {
        this.opeCdLoading = false;
      }
    },

    // 查询委托单位代码（弹框表单）
    async queryMerchIdForm(queryString, cb) {
      this.merchIdLoading = true;
      try {
        const params = {
          merchId: queryString,
          opeCd: this.form.opeCd // 使用表单中的业务代码进行联动查询
        };
        const { code, data } = await listGroupByMerchId(params);
        if (code === '0' && data) {
          const results = data.map(item => ({
            value: item.merchId,
            label: item.prdtNm,
            item: item
          }));
          cb(results);
        } else {
          cb([]);
        }
      } catch (error) {
        console.error('Error fetching merchId:', error);
        cb([]);
      } finally {
        this.merchIdLoading = false;
      }
    },

    // 选择业务代码（搜索区域）
    handleOpeCdSelect(item) {
      this.query.opeCd = item.value;
      this.query.opeNm = item.item.opeNm;
      // 清空委托单位代码，因为业务代码变化时需要重新选择委托单位
      this.query.merchId = '';
      this.query.merchNm = '';
    },

    // 选择委托单位代码（搜索区域）
    handleMerchIdSelect(item) {
      this.query.merchId = item.value;
      this.query.merchNm = item.item.prdtNm;
    },

    // 清除业务代码（搜索区域）
    handleOpeCdClear() {
      this.query.opeCd = '';
      this.query.opeNm = '';
      // 同时清除委托单位代码
      this.query.merchId = '';
      this.query.merchNm = '';
    },

    // 清除委托单位代码（搜索区域）
    handleMerchIdClear() {
      this.query.merchId = '';
      this.query.merchNm = '';
    },

    // 选择业务代码（弹框表单）
    handleOpeCdFormSelect(item) {
      this.form.opeCd = item.value;
      this.form.opeNm = item.item.opeNm;
      // 清空委托单位代码，因为业务代码变化时需要重新选择委托单位
      this.form.merchId = '';
      this.form.merchNm = '';
      // 清除表单验证错误
      this.$nextTick(() => {
        if (this.$refs.crud && this.$refs.crud.$refs.dialogForm) {
          this.$refs.crud.$refs.dialogForm.clearValidate('opeCd');
          this.$refs.crud.$refs.dialogForm.clearValidate('merchId');
        }
      });
    },

    // 选择委托单位代码（弹框表单）
    handleMerchIdFormSelect(item) {
      this.form.merchId = item.value;
      this.form.merchNm = item.item.prdtNm;
      // 清除表单验证错误
      this.$nextTick(() => {
        if (this.$refs.crud && this.$refs.crud.$refs.dialogForm) {
          this.$refs.crud.$refs.dialogForm.clearValidate('merchId');
        }
      });
    },

    // 清除业务代码（弹框表单）
    handleOpeCdFormClear() {
      this.form.opeCd = '';
      this.form.opeNm = '';
      // 同时清除委托单位代码
      this.form.merchId = '';
      this.form.merchNm = '';
      // 清除表单验证错误
      this.$nextTick(() => {
        if (this.$refs.crud && this.$refs.crud.$refs.dialogForm) {
          this.$refs.crud.$refs.dialogForm.clearValidate('opeCd');
          this.$refs.crud.$refs.dialogForm.clearValidate('merchId');
        }
      });
    },

    // 清除委托单位代码（弹框表单）
    handleMerchIdFormClear() {
      this.form.merchId = '';
      this.form.merchNm = '';
      // 清除表单验证错误
      this.$nextTick(() => {
        if (this.$refs.crud && this.$refs.crud.$refs.dialogForm) {
          this.$refs.crud.$refs.dialogForm.clearValidate('merchId');
        }
      });
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.custom-input {
  width: 200px !important;
}

.input-with-tip {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 查询区域样式优化 */
:deep(.yo-table__search .el-form-item) {
  margin-bottom: 18px;
}

:deep(.yo-table__search .el-input) {
  width: 200px;
}

:deep(.yo-table__search .el-autocomplete) {
  width: 200px;
}

.tip-text {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.autocomplete-item {
  display: flex;
  flex-direction: column;
}
.autocomplete-item .value {
  font-size: 14px;
  color: #606266;
}
.autocomplete-item .name {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.autocomplete-item .highlighted .addr {
  color: #ddd;
}

/* 搜索按钮区域样式 */
:deep(.yo-table__search-menu) {
  text-align: right;
  padding-top: 10px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

:deep(.yo-table__search-menu .el-button) {
  margin-left: 8px;
}

/* 查询区域整体布局优化 */
:deep(.yo-table__search) {
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 20px;
}

:deep(.yo-table__search .el-row) {
  margin-bottom: 0;
}

:deep(.yo-table__search .el-col) {
  padding-right: 20px;
  margin-bottom: 18px;
}

/* 确保选中记录框有足够空间 */
:deep(.yo-table__menu-left) {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  min-height: 40px;
}

:deep(.yo-table__menu-left .el-button) {
  margin-right: 8px;
  margin-bottom: 0;
}

/* 表格头部操作区域样式 */
:deep(.yo-table__menu) {
  padding: 16px 0;
  background: #fff;
  border-bottom: 1px solid #ebeef5;
}

/* 响应式布局优化 */
@media (max-width: 1200px) {
  :deep(.yo-table__search .el-col) {
    padding-right: 10px;
  }

  .custom-input {
    width: 180px !important;
  }

  :deep(.yo-table__search .el-input) {
    width: 180px;
  }

  :deep(.yo-table__search .el-autocomplete) {
    width: 180px;
  }
}
</style>